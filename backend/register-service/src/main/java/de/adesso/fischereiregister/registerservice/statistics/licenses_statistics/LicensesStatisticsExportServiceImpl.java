package de.adesso.fischereiregister.registerservice.statistics.licenses_statistics;

import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.registerservice.export.ExportContent;
import de.adesso.fischereiregister.registerservice.export.ExportContentType;
import de.adesso.fischereiregister.view.licenses_statistics.persistance.LicensesStatisticsView;
import de.adesso.fischereiregister.view.licenses_statistics.services.LicensesStatisticsViewService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.List;

@Service
@Slf4j
@AllArgsConstructor
public class LicensesStatisticsExportServiceImpl implements LicensesStatisticsExportService {

    private static final String FILENAME_PREFIX = "licenses-statistics";
    private static final String DATETIME_PATTERN = "yyyyMMdd-HHmmss";

    private final LicensesStatisticsViewService licensesStatisticsViewService;

    @Override
    public ExportContent exportLicensesStatistics(
            LicenseType licenseType,
            List<Integer> years,
            String office,
            String federalState) {
        try {
            log.info("Exporting licenses statistics for licenseType: {}, years: {}, office: {}, federalState: {}",
                    licenseType, years, office, federalState);

            // If years list is empty, get all available years
            final List<Integer> yearsToQuery = years != null && !years.isEmpty() ? years : licensesStatisticsViewService.getAvailableYears();

            final ExportContent exportContent = generateLicensesStatisticsCsv(licenseType, federalState, office, yearsToQuery);

            log.info("Licenses statistics exported: {}", exportContent.getFullFilename());

            return exportContent;
        } catch (Exception e) {
            log.error("Error exporting licenses statistics for licenseType {}, years {}, office {}, federalState {}: {}",
                    licenseType, years, office, federalState, e.getMessage(), e);
            throw new RuntimeException("Failed to export licenses statistics", e);
        }
    }

    private ExportContent generateLicensesStatisticsCsv(LicenseType licenseType, String federalState, String office, List<Integer> years) throws IOException {
        StringWriter stringWriter = new StringWriter();
        CSVFormat csvFormat = CSVFormat.DEFAULT.builder()
                .setHeader("Jahr", "Bundesland", "Behörde", "Scheintyp", "Antragsweg", "Anzahl")
                .build();

        List<LicensesStatisticsView> licensesStatisticsViews;

        // if federalState is provided the office filter is ignored
        if (federalState != null && !federalState.isEmpty()) {
            licensesStatisticsViews = licensesStatisticsViewService.getStatisticsByLicenseTypeAndFederalStateAndYears(licenseType, federalState, years);
        } else if (office != null && !office.isEmpty()) {
            licensesStatisticsViews = licensesStatisticsViewService.getStatisticsByLicenseTypeAndOfficeAndYears(licenseType, office, years);
        } else {
            // Get data for all federal states
            licensesStatisticsViews = licensesStatisticsViewService.getStatisticsByLicenseTypeAndYears(licenseType, years);
        }

        try (CSVPrinter csvPrinter = new CSVPrinter(stringWriter, csvFormat)) {
            licensesStatisticsViews.stream()
                    .sorted(Comparator.comparing(LicensesStatisticsView::getYear).reversed())
                    .forEach(entry -> {
                        try {
                            csvPrinter.printRecord(
                                    entry.getYear(),
                                    entry.getFederalState(),
                                    entry.getOffice(),
                                    entry.getLicenseType(),
                                    entry.getSource(),
                                    entry.getCount()
                            );
                        } catch (IOException e) {
                            throw new RuntimeException("Error writing CSV record", e);
                        }
                    });
        }

        String filename = buildFilename(licenseType, office, federalState);
        byte[] content = stringWriter.toString().getBytes(StandardCharsets.UTF_8);

        return new ExportContent(filename, ExportContentType.CSV, content);
    }

    private String buildFilename(LicenseType licenseType, String office, String federalState) {
        StringBuilder filename = new StringBuilder(FILENAME_PREFIX);

        if (licenseType != null) {
            filename.append("-").append(licenseType.toString().toLowerCase());
        }

        if (federalState != null && !federalState.isEmpty()) {
            filename.append("-").append(federalState);
        }

        if (office != null && !office.isEmpty()) {
            filename.append("-").append(office.replaceAll("[^a-zA-Z0-9]", "_"));
        }

        filename.append("-").append(getCurrentTimestamp());

        return filename.toString();
    }

    private String getCurrentTimestamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern(DATETIME_PATTERN));
    }
}
