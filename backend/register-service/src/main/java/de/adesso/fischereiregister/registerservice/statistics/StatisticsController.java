package de.adesso.fischereiregister.registerservice.statistics;

import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.registerservice.domain.mapper.BansStatisticsMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.CertificationsStatisticsMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.ErrorsStatisticsMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.InspectionsStatisticsMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.LicensesStatisticsMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.TaxesStatisticsMapper;
import de.adesso.fischereiregister.registerservice.statistics.bans_statistics.BansStatistics;
import de.adesso.fischereiregister.registerservice.statistics.bans_statistics.BansStatisticsService;
import de.adesso.fischereiregister.registerservice.statistics.certifications_statistics.CertificationsStatistics;
import de.adesso.fischereiregister.registerservice.statistics.certifications_statistics.CertificationsStatisticsService;
import de.adesso.fischereiregister.registerservice.statistics.errors_statistics.ErrorsStatistics;
import de.adesso.fischereiregister.registerservice.statistics.errors_statistics.ErrorsStatisticsService;
import de.adesso.fischereiregister.registerservice.statistics.inspections_statistics.InspectionsStatistics;
import de.adesso.fischereiregister.registerservice.statistics.inspections_statistics.InspectionsStatisticsService;
import de.adesso.fischereiregister.registerservice.statistics.licenses_statistics.LicensesStatistics;
import de.adesso.fischereiregister.registerservice.statistics.licenses_statistics.LicensesStatisticsService;
import de.adesso.fischereiregister.registerservice.statistics.taxes_statistics.TaxesStatistics;
import de.adesso.fischereiregister.registerservice.statistics.taxes_statistics.TaxesStatisticsService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.openapitools.model.FederalStateAbbreviation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping
@Slf4j
@AllArgsConstructor
public class StatisticsController implements api.StatisticsApi {

    private final LicensesStatisticsService licensesStatisticsService;
    private final TaxesStatisticsService taxesStatisticsService;
    private final BansStatisticsService bansStatisticsService;
    private final CertificationsStatisticsService certificationsStatisticsService;
    private final InspectionsStatisticsService inspectionsStatisticsService;
    private final ErrorsStatisticsService errorsStatisticsService;

    @Override
    public ResponseEntity<List<org.openapitools.model.LicensesStatistics>> statisticsControllerGetRegularLicensesStatistics(List<Integer> year, String office, FederalStateAbbreviation federalState) {
        return ResponseEntity.ok(getLicensesStatisticsForLicenseType(LicenseType.REGULAR, year, office, federalState));
    }

    @Override
    public ResponseEntity<List<org.openapitools.model.LicensesStatistics>> statisticsControllerGetLimitedLicensesStatistics(List<Integer> year, String office, FederalStateAbbreviation federalState) {
        return ResponseEntity.ok(getLicensesStatisticsForLicenseType(LicenseType.LIMITED, year, office, federalState));
    }

    @Override
    public ResponseEntity<List<org.openapitools.model.LicensesStatistics>> statisticsControllerGetVacationLicensesStatistics(List<Integer> year, String office, FederalStateAbbreviation federalState) {
        return ResponseEntity.ok(getLicensesStatisticsForLicenseType(LicenseType.VACATION, year, office, federalState));
    }

    private List<org.openapitools.model.LicensesStatistics> getLicensesStatisticsForLicenseType(LicenseType licenseType, List<Integer> years, String office, FederalStateAbbreviation federalState) {
        try {
            String federalStateValue = federalState != null ? federalState.getValue() : null;
            final List<LicensesStatistics> licensesStatistics = licensesStatisticsService.getLicensesStatistics(licenseType, years, office, federalStateValue);
            return LicensesStatisticsMapper.INSTANCE.licensesStatisticsListToApiLicensesStatisticsList(licensesStatistics);
        } catch (Exception e) {
            log.error("Error fetching licensesStatistics for licenseType {}: {}", licenseType, e.getMessage(), e);
            throw new RuntimeException("Failed to fetch licensesStatistics for licenseType " + licenseType, e);
        }
    }

    @Override
    public ResponseEntity<List<org.openapitools.model.TaxesStatistics>> statisticsControllerGetTaxesStatistics(List<Integer> year, String office, FederalStateAbbreviation federalState) {
        try {
            String federalStateValue = federalState != null ? federalState.getValue() : null;
            final List<TaxesStatistics> taxesStatistics = taxesStatisticsService.getTaxesStatistics(year, office, federalStateValue);
            return ResponseEntity.ok(TaxesStatisticsMapper.INSTANCE.taxesStatisticsListToApiTaxesStatisticsList(taxesStatistics));
        } catch (Exception e) {
            log.error("Error fetching taxesStatistics: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to fetch taxesStatistics", e);
        }
    }

    @Override
    public ResponseEntity<List<org.openapitools.model.BansStatistics>> statisticsControllerGetBansStatistics(List<Integer> year, FederalStateAbbreviation federalState) {
        try {
            // If years list is empty, get all available years
            final List<Integer> yearsToQuery = year != null && !year.isEmpty() ? year : bansStatisticsService.getAvailableYears();

            List<BansStatistics> bansStatistics;

            if (federalState != null) {
                // If federalState is provided
                String federalStateValue = federalState.getValue();
                log.info("Fetching ban statistics with federalState: {} and years: {}", federalStateValue, yearsToQuery);
                bansStatistics = bansStatisticsService.getStatisticsByFederalStateAndYears(federalStateValue, yearsToQuery);
            } else {
                // If no federalState specified
                log.info("Fetching ban statistics for all regions and years: {}", yearsToQuery);
                bansStatistics = bansStatisticsService.getStatisticsByYears(yearsToQuery);
            }

            log.info("Successfully fetched {} ban statistics", bansStatistics.size());

            return ResponseEntity.ok(BansStatisticsMapper.INSTANCE.bansStatisticsListToApiBansStatisticsList(bansStatistics));

        } catch (Exception e) {
            log.error("Error fetching ban statistics: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to fetch ban statistics", e);
        }
    }

    @Override
    public ResponseEntity<List<org.openapitools.model.CertificationsStatistics>> statisticsControllerGetCertificationsStatistics(List<Integer> year, String office, FederalStateAbbreviation federalState) {
        try {
            String federalStateValue = federalState != null ? federalState.getValue() : null;
            final List<CertificationsStatistics> certificationsStatistics = certificationsStatisticsService.getCertificationsStatistics(year, office, federalStateValue);
            return ResponseEntity.ok(CertificationsStatisticsMapper.INSTANCE.certificationsStatisticsListToApiCertificationsStatisticsList(certificationsStatistics));
        } catch (Exception e) {
            log.error("Error fetching certificationsStatistics: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to fetch certificationsStatistics", e);
        }
    }

    @Override
    public ResponseEntity<List<org.openapitools.model.InspectionsStatistics>> statisticsControllerGetInspectionsStatistics(@Valid List<Integer> year, @Valid FederalStateAbbreviation federalState) {
        try {
            String federalStateValue = federalState != null ? federalState.getValue() : null;
            final List<InspectionsStatistics> inspectionsStatistics = inspectionsStatisticsService.getInspectionsStatistics(year, federalStateValue);
            return ResponseEntity.ok(InspectionsStatisticsMapper.INSTANCE.inspectionsStatisticsListToApiInspectionsStatisticsList(inspectionsStatistics));
        } catch (Exception e) {
            log.error("Error fetching inspectionsStatistics: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to fetch inspectionsStatistics", e);
        }
    }

    @Override
    public ResponseEntity<Integer> statisticsControllerGetActiveBansStatistics(FederalStateAbbreviation federalState) {
        try {
            Integer activeBansCount;

            if (federalState != null) {
                // If federalState is provided, get active bans for that specific federal state
                log.info("Fetching active bans count for federalState: {}", federalState.getValue());
                activeBansCount = bansStatisticsService.getActiveBansAmountByFederalState(federalState.getValue());
            } else {
                // If no federalState specified, get total active bans count
                log.info("Fetching total active bans count for all federal states");
                activeBansCount = bansStatisticsService.getActiveBansAmount();
            }

            log.info("Successfully fetched active bans count: {}", activeBansCount);

            return ResponseEntity.ok(activeBansCount);

        } catch (Exception e) {
            log.error("Error fetching active bans statistics: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to fetch active bans statistics", e);
        }
    }

    /**
     * Fetches error statistics based on the provided years, office, and federal state.
     * If no years are provided, it defaults to all available years.
     *
     * @param year          List of years to filter the statistics.
     * @param office        Office to filter the statistics (optional).
     * @param federalState  Federal state abbreviation to filter the statistics (optional).
     * @return ResponseEntity containing a list of ErrorsStatistics.
     */
    @Override
    public ResponseEntity<List<org.openapitools.model.ErrorsStatistics>> statisticsControllerGetErrorsStatistics(@Valid List<Integer> year, @Valid String office, @Valid FederalStateAbbreviation federalState) {
        try {
            List<ErrorsStatistics> errorsStatisticsList;

            if (office != null || federalState != null) {
                errorsStatisticsList = errorsStatisticsService.getStatisticsByFederalStateAndOfficeAndYears(
                    federalState != null ? federalState.getValue() : null,
                    office,
                    year
                );
            } else {
                errorsStatisticsList = errorsStatisticsService.getStatisticsByYears(year);
            }

            return ResponseEntity.ok(ErrorsStatisticsMapper.INSTANCE.toResponse(errorsStatisticsList));
        } catch (Exception e) {
            log.error("Error fetching error statistics: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to fetch error statistics", e);
        }
    }
}
