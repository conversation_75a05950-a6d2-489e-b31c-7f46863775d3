package de.adesso.fischereiregister.registerservice.statistics.errors_statistics;

import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.ports.ErrorsProtocolStatisticsPort;
import de.adesso.fischereiregister.registerservice.export.ExportContent;
import de.adesso.fischereiregister.registerservice.export.ExportContentType;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
@Slf4j
@AllArgsConstructor
public class ErrorsStatisticsExportServiceImpl implements ErrorsStatisticsExportService {

    private static final String FILENAME_PREFIX = "errors-statistics";
    private static final String DATETIME_PATTERN = "yyyyMMdd-HHmmss";

    private final ErrorsProtocolStatisticsPort errorsProtocolStatisticsPort;

    @Override
    public ExportContent exportErrorsStatistics(String federalState, String office, List<Integer> years) {
        try {
            log.debug("Exporting errors statistics for federalState: {}, office: {}, years: {}",
                    federalState, office, years);

            // If years list is empty, get all available years
            final List<Integer> yearsToQuery = years != null && !years.isEmpty() ? years : errorsProtocolStatisticsPort.getAvailableYears();

            final ExportContent exportContent = generateErrorsStatisticsCsv(federalState, office, yearsToQuery);

            log.info("errors statistics exported: {}", exportContent.getFullFilename());

            return exportContent;

        } catch (Exception e) {
            log.error("Error exporting errors statistics for federalState {}, office {}, years {}: {}",
                    federalState, office, years, e.getMessage(), e);
            throw new RuntimeException("Failed to export errors statistics", e);
        }
    }

    private ExportContent generateErrorsStatisticsCsv(String federalState, String office, List<Integer> years) throws IOException {
        StringWriter stringWriter = new StringWriter();
        CSVFormat csvFormat = CSVFormat.DEFAULT.builder()
                .setHeader("Jahr", "Bundesland", "Online Service Fehler", "Karten Fehler", "System Fehler")
                .build();

        try (CSVPrinter csvPrinter = new CSVPrinter(stringWriter, csvFormat)) {
            if (federalState != null && !federalState.isEmpty()) {
                // Export for specific federal state
                for (Integer year : years) {
                    int onlineServiceErrors = errorsProtocolStatisticsPort.getOnlineServiceErrorsAmountByYearAndFederalStateAndOffice(year, federalState, office);
                    int cardErrors = errorsProtocolStatisticsPort.getCardOrderErrorsAmountByYearAndFederalStateAndOffice(year, federalState, office);
                    int systemErrors = errorsProtocolStatisticsPort.getSystemErrorsAmountByYearAndFederalStateAndOffice(year, federalState, office);

                    csvPrinter.printRecord(
                            year,
                            federalState,
                            onlineServiceErrors,
                            cardErrors,
                            systemErrors
                    );
                }
            } else {
                // Export for all federal states
                for (FederalState fs : FederalState.values()) {
                    for (Integer year : years) {
                        int onlineServiceErrors = errorsProtocolStatisticsPort.getOnlineServiceErrorsAmountByYearAndFederalStateAndOffice(year, fs.name(), office);
                        int cardErrors = errorsProtocolStatisticsPort.getCardOrderErrorsAmountByYearAndFederalStateAndOffice(year, fs.name(), office);
                        int systemErrors = errorsProtocolStatisticsPort.getSystemErrorsAmountByYearAndFederalStateAndOffice(year, fs.name(), office);

                        csvPrinter.printRecord(
                                fs.name(),
                                year,
                                onlineServiceErrors,
                                cardErrors,
                                systemErrors
                        );
                    }
                }
            }
        }

        String filename = buildFilename(federalState, office);
        byte[] content = stringWriter.toString().getBytes(StandardCharsets.UTF_8);

        return new ExportContent(filename, ExportContentType.CSV, content);
    }

    private String buildFilename(String federalState, String office) {
        StringBuilder filename = new StringBuilder(FILENAME_PREFIX);

        if (federalState != null && !federalState.isEmpty()) {
            filename.append("-").append(federalState);
        }

        if (office != null && !office.isEmpty()) {
            filename.append("-").append(office.replaceAll("[^a-zA-Z0-9]", "_"));
        }

        filename.append("-").append(getCurrentTimestamp());

        return filename.toString();
    }

    private String getCurrentTimestamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern(DATETIME_PATTERN));
    }
}
